import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { checkBookReadAccess } from '@/lib/utils/book-access'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ bookId: string }> }
) {
  try {
    const { bookId } = await params
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const chapterId = searchParams.get('chapterId')

    // Check if user has access to this book
    const accessResult = await checkBookReadAccess(supabase, user.id, bookId)
    if (!accessResult.hasAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Build query for book audio posts
    let query = supabase
      .from('book_audio_posts')
      .select(`
        id,
        audio_url,
        description,
        duration_seconds,
        chapter_position,
        page_context,
        love_count,
        reply_count,
        created_at,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url
        )
      `)
      .eq('project_id', params.bookId)
      .order('created_at', { ascending: false })

    // Filter by chapter if specified
    if (chapterId) {
      query = query.eq('chapter_id', chapterId)
    }

    const { data: posts, error } = await query

    if (error) {
      console.error('Error fetching book audio posts:', error)
      return NextResponse.json({ error: 'Failed to fetch posts' }, { status: 500 })
    }

    return NextResponse.json({ posts: posts || [] })
  } catch (error) {
    console.error('Error in book audio posts API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { bookId: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has access to this book
    const accessResult = await checkBookReadAccess(supabase, user.id, params.bookId)
    if (!accessResult.hasAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    const body = await request.json()
    const { 
      chapterId, 
      audioUrl, 
      audioKey, 
      duration, 
      description, 
      chapterPosition, 
      pageContext 
    } = body

    // Validate required fields
    if (!chapterId || !audioUrl || !audioKey || !duration) {
      return NextResponse.json({ 
        error: 'Missing required fields: chapterId, audioUrl, audioKey, duration' 
      }, { status: 400 })
    }

    if (duration > 9.0) {
      return NextResponse.json({ 
        error: 'Duration must be 9 seconds or less' 
      }, { status: 400 })
    }

    if (description && description.length > 50) {
      return NextResponse.json({ 
        error: 'Description cannot exceed 50 characters' 
      }, { status: 400 })
    }

    // Verify chapter belongs to this book
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .select('id')
      .eq('id', chapterId)
      .eq('project_id', params.bookId)
      .single()

    if (chapterError || !chapter) {
      return NextResponse.json({ 
        error: 'Invalid chapter for this book' 
      }, { status: 400 })
    }

    // Create book audio post
    const { data: post, error } = await supabase
      .from('book_audio_posts')
      .insert({
        user_id: user.id,
        project_id: params.bookId,
        chapter_id: chapterId,
        audio_url: audioUrl,
        audio_key: audioKey,
        duration_seconds: duration,
        file_size_bytes: 0, // Default value
        description: description || null,
        chapter_position: chapterPosition || 0,
        page_context: pageContext || null
      })
      .select(`
        id,
        audio_url,
        description,
        duration_seconds,
        chapter_position,
        page_context,
        love_count,
        reply_count,
        created_at,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url
        )
      `)
      .single()

    if (error) {
      console.error('Error creating book audio post:', error)
      return NextResponse.json({ error: 'Failed to create post' }, { status: 500 })
    }

    return NextResponse.json({ post })
  } catch (error) {
    console.error('Error in book audio posts API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
